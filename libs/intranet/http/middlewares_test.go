package http

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/emilioforrer/hexa/httpsvr"
)

func TestGenerateTraceID(t *testing.T) {
	tests := []struct {
		name   string
		verify func(t *testing.T, traceID string)
	}{
		{
			name: "generates valid trace ID",
			verify: func(t *testing.T, traceID string) {
				if traceID == "" {
					t.<PERSON><PERSON>("Expected non-empty trace ID")
				}
				if len(traceID) != 32 {
					t.<PERSON><PERSON>("Expected trace ID length 32, got %d", len(traceID))
				}
			},
		},
		{
			name: "generates different trace IDs",
			verify: func(t *testing.T, traceID string) {
				traceID2 := generateTraceID()
				if traceID == traceID2 {
					t.<PERSON>rror("Expected different trace IDs")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			traceID := generateTraceID()
			if tt.verify != nil {
				tt.verify(t, traceID)
			}
		})
	}
}

func TestGetTraceID(t *testing.T) {
	tests := []struct {
		name     string
		ctx      context.Context
		expected string
		verify   func(t *testing.T, got string, expected string)
	}{
		{
			name:     "context with trace ID",
			ctx:      context.WithValue(context.Background(), traceIDKey, "test-trace-id"),
			expected: "test-trace-id",
			verify: func(t *testing.T, got string, expected string) {
				if got != expected {
					t.Errorf("Expected trace ID %s, got %s", expected, got)
				}
			},
		},
		{
			name:     "context without trace ID",
			ctx:      context.Background(),
			expected: "",
			verify: func(t *testing.T, got string, expected string) {
				if got == "" {
					t.Error("Expected generated trace ID when none exists")
				}
				if len(got) != 32 {
					t.Errorf("Expected generated trace ID length 32, got %d", len(got))
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getTraceID(tt.ctx)
			if tt.verify != nil {
				tt.verify(t, got, tt.expected)
			}
		})
	}
}

func TestIsServerError(t *testing.T) {
	tests := []struct {
		name     string
		code     int
		expected bool
	}{
		{"500 is server error", 500, true},
		{"502 is server error", 502, true},
		{"599 is server error", 599, true},
		{"400 is not server error", 400, false},
		{"404 is not server error", 404, false},
		{"200 is not server error", 200, false},
		{"600 is not server error", 600, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := isServerError(tt.code)
			if got != tt.expected {
				t.Errorf("isServerError(%d) = %v, want %v", tt.code, got, tt.expected)
			}
		})
	}
}

func TestIsClientError(t *testing.T) {
	tests := []struct {
		name     string
		code     int
		expected bool
	}{
		{"400 is client error", 400, true},
		{"401 is client error", 401, true},
		{"403 is client error", 403, true},
		{"404 is not client error (excluded)", 404, false},
		{"499 is client error", 499, true},
		{"500 is not client error", 500, false},
		{"200 is not client error", 200, false},
		{"399 is not client error", 399, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := isClientError(tt.code)
			if got != tt.expected {
				t.Errorf("isClientError(%d) = %v, want %v", tt.code, got, tt.expected)
			}
		})
	}
}

func TestNotFoundMiddleware(t *testing.T) {
	tests := []struct {
		name           string
		handlerStatus  int
		expectedStatus int
		expectedBody   string
		verify         func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int)
	}{
		{
			name:           "404 response gets custom JSON",
			handlerStatus:  http.StatusNotFound,
			expectedStatus: http.StatusNotFound,
			expectedBody:   "Route not found",
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int) {
				if w.Header().Get("Content-Type") != "application/json" {
					t.Error("Expected Content-Type to be application/json")
				}

				var response httpsvr.ErrorResponse
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				if len(response.Errors) == 0 {
					t.Error("Expected errors array to be populated")
				}

				if response.Errors[0].Status != "404" {
					t.Errorf("Expected status 404, got %s", response.Errors[0].Status)
				}
			},
		},
		{
			name:           "200 response passes through",
			handlerStatus:  http.StatusOK,
			expectedStatus: http.StatusOK,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int) {
				if w.Code != handlerStatus {
					t.Errorf("Expected status %d, got %d", handlerStatus, w.Code)
				}
			},
		},
		{
			name:           "500 response passes through",
			handlerStatus:  http.StatusInternalServerError,
			expectedStatus: http.StatusInternalServerError,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int) {
				if w.Code != handlerStatus {
					t.Errorf("Expected status %d, got %d", handlerStatus, w.Code)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a test handler that returns the specified status
			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.handlerStatus)
				w.Write([]byte("test response"))
			})

			// Wrap with middleware
			middleware := NotFoundMiddleware()
			wrappedHandler := middleware(handler)

			// Create request and response recorder
			req := httptest.NewRequest("GET", "/test", nil)
			w := httptest.NewRecorder()

			// Execute request
			wrappedHandler.ServeHTTP(w, req)

			// Custom verification
			if tt.verify != nil {
				tt.verify(t, w, tt.handlerStatus)
			}
		})
	}
}

func TestInternalServerErrorMiddleware(t *testing.T) {
	tests := []struct {
		name           string
		handlerStatus  int
		expectedStatus int
		verify         func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int)
	}{
		{
			name:           "500 response gets custom JSON",
			handlerStatus:  http.StatusInternalServerError,
			expectedStatus: http.StatusInternalServerError,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int) {
				if w.Header().Get("Content-Type") != "application/json" {
					t.Error("Expected Content-Type to be application/json")
				}

				var response httpsvr.ErrorResponse
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				if len(response.Errors) == 0 {
					t.Error("Expected errors array to be populated")
				}

				if response.Errors[0].Status != "500" {
					t.Errorf("Expected status 500, got %s", response.Errors[0].Status)
				}
			},
		},
		{
			name:           "502 response gets custom JSON",
			handlerStatus:  http.StatusBadGateway,
			expectedStatus: http.StatusBadGateway,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int) {
				if w.Header().Get("Content-Type") != "application/json" {
					t.Error("Expected Content-Type to be application/json")
				}
			},
		},
		{
			name:           "200 response passes through",
			handlerStatus:  http.StatusOK,
			expectedStatus: http.StatusOK,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int) {
				if w.Code != handlerStatus {
					t.Errorf("Expected status %d, got %d", handlerStatus, w.Code)
				}
			},
		},
		{
			name:           "404 response passes through",
			handlerStatus:  http.StatusNotFound,
			expectedStatus: http.StatusNotFound,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int) {
				if w.Code != handlerStatus {
					t.Errorf("Expected status %d, got %d", handlerStatus, w.Code)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a test handler that returns the specified status
			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.handlerStatus)
				w.Write([]byte("test response"))
			})

			// Wrap with middleware
			middleware := InternalServerErrorMiddleware()
			wrappedHandler := middleware(handler)

			// Create request and response recorder
			req := httptest.NewRequest("GET", "/test", nil)
			w := httptest.NewRecorder()

			// Execute request
			wrappedHandler.ServeHTTP(w, req)

			// Custom verification
			if tt.verify != nil {
				tt.verify(t, w, tt.handlerStatus)
			}
		})
	}
}

func TestClientErrorMiddleware(t *testing.T) {
	tests := []struct {
		name           string
		handlerStatus  int
		handlerBody    string
		expectedStatus int
		verify         func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int, handlerBody string)
	}{
		{
			name:           "400 response gets custom JSON",
			handlerStatus:  http.StatusBadRequest,
			handlerBody:    "Bad request error",
			expectedStatus: http.StatusBadRequest,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int, handlerBody string) {
				if w.Header().Get("Content-Type") != "application/json" {
					t.Error("Expected Content-Type to be application/json")
				}

				var response httpsvr.ErrorResponse
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				if len(response.Errors) == 0 {
					t.Error("Expected errors array to be populated")
				}

				if response.Errors[0].Status != "400" {
					t.Errorf("Expected status 400, got %s", response.Errors[0].Status)
				}

				if !strings.Contains(response.Errors[0].Detail, handlerBody) {
					t.Errorf("Expected detail to contain %s, got %s", handlerBody, response.Errors[0].Detail)
				}
			},
		},
		{
			name:           "404 response passes through (excluded)",
			handlerStatus:  http.StatusNotFound,
			handlerBody:    "Not found",
			expectedStatus: http.StatusNotFound,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int, handlerBody string) {
				if w.Code != handlerStatus {
					t.Errorf("Expected status %d, got %d", handlerStatus, w.Code)
				}
			},
		},
		{
			name:           "200 response passes through",
			handlerStatus:  http.StatusOK,
			handlerBody:    "Success",
			expectedStatus: http.StatusOK,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, handlerStatus int, handlerBody string) {
				if w.Code != handlerStatus {
					t.Errorf("Expected status %d, got %d", handlerStatus, w.Code)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a test handler that returns the specified status and body
			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.handlerStatus)
				w.Write([]byte(tt.handlerBody))
			})

			// Wrap with middleware
			middleware := ClientErrorMiddleware()
			wrappedHandler := middleware(handler)

			// Create request and response recorder
			req := httptest.NewRequest("GET", "/test", nil)
			w := httptest.NewRecorder()

			// Execute request
			wrappedHandler.ServeHTTP(w, req)

			// Custom verification
			if tt.verify != nil {
				tt.verify(t, w, tt.handlerStatus, tt.handlerBody)
			}
		})
	}
}

func TestTracingMiddleware(t *testing.T) {
	tests := []struct {
		name   string
		verify func(t *testing.T, ctx context.Context)
	}{
		{
			name: "adds trace ID to context",
			verify: func(t *testing.T, ctx context.Context) {
				traceID := getTraceID(ctx)
				if traceID == "" {
					t.Error("Expected trace ID to be set in context")
				}
				if len(traceID) != 32 {
					t.Errorf("Expected trace ID length 32, got %d", len(traceID))
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var capturedCtx context.Context

			// Create a test handler that captures the context
			handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				capturedCtx = r.Context()
				w.WriteHeader(http.StatusOK)
			})

			// Wrap with middleware
			middleware := TracingMiddleware()
			wrappedHandler := middleware(handler)

			// Create request and response recorder
			req := httptest.NewRequest("GET", "/test", nil)
			w := httptest.NewRecorder()

			// Execute request
			wrappedHandler.ServeHTTP(w, req)

			// Custom verification
			if tt.verify != nil {
				tt.verify(t, capturedCtx)
			}
		})
	}
}

func TestSkipMiddlewaresForPaths(t *testing.T) {
	// Create a test middleware that adds a header
	testMiddleware := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("X-Test-Middleware", "applied")
			next.ServeHTTP(w, r)
		})
	}

	// Create a simple handler
	handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	tests := []struct {
		name             string
		skipPatterns     []string
		requestPath      string
		expectMiddleware bool
		verify           func(t *testing.T, w *httptest.ResponseRecorder, expectMiddleware bool)
	}{
		{
			name:             "middleware applied for non-matching path",
			skipPatterns:     []string{"/skip"},
			requestPath:      "/test",
			expectMiddleware: true,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, expectMiddleware bool) {
				header := w.Header().Get("X-Test-Middleware")
				if expectMiddleware && header != "applied" {
					t.Error("Expected middleware to be applied")
				}
				if !expectMiddleware && header == "applied" {
					t.Error("Expected middleware to be skipped")
				}
			},
		},
		{
			name:             "middleware skipped for matching path",
			skipPatterns:     []string{"/skip"},
			requestPath:      "/skip",
			expectMiddleware: false,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, expectMiddleware bool) {
				header := w.Header().Get("X-Test-Middleware")
				if expectMiddleware && header != "applied" {
					t.Error("Expected middleware to be applied")
				}
				if !expectMiddleware && header == "applied" {
					t.Error("Expected middleware to be skipped")
				}
			},
		},
		{
			name:             "middleware skipped for regex pattern",
			skipPatterns:     []string{"/api/.*"},
			requestPath:      "/api/test",
			expectMiddleware: false,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, expectMiddleware bool) {
				header := w.Header().Get("X-Test-Middleware")
				if expectMiddleware && header != "applied" {
					t.Error("Expected middleware to be applied")
				}
				if !expectMiddleware && header == "applied" {
					t.Error("Expected middleware to be skipped")
				}
			},
		},
		{
			name:             "middleware applied for non-matching regex",
			skipPatterns:     []string{"/api/.*"},
			requestPath:      "/web/test",
			expectMiddleware: true,
			verify: func(t *testing.T, w *httptest.ResponseRecorder, expectMiddleware bool) {
				header := w.Header().Get("X-Test-Middleware")
				if expectMiddleware && header != "applied" {
					t.Error("Expected middleware to be applied")
				}
				if !expectMiddleware && header == "applied" {
					t.Error("Expected middleware to be skipped")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Wrap with skip middleware
			skipMiddleware := SkipMiddlewaresForPaths(tt.skipPatterns, testMiddleware)
			wrappedHandler := skipMiddleware(handler)

			// Create request and response recorder
			req := httptest.NewRequest("GET", tt.requestPath, nil)
			w := httptest.NewRecorder()

			// Execute request
			wrappedHandler.ServeHTTP(w, req)

			// Check status
			if w.Code != http.StatusOK {
				t.Errorf("Expected status 200, got %d", w.Code)
			}

			// Custom verification
			if tt.verify != nil {
				tt.verify(t, w, tt.expectMiddleware)
			}
		})
	}
}
