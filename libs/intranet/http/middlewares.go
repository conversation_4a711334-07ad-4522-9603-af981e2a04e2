// Package http provides HTTP middleware implementations for the SA Intranet application.
// It includes authentication, authorization, tracing, CORS, and other essential middleware
// components for secure and observable HTTP request processing.
package http

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"log/slog"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"sa-intranet/policies"
	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"

	"github.com/emilioforrer/hexa/httpsvr"
	"github.com/samber/do"

	"go.opentelemetry.io/otel/trace"
)

// contextKey is a custom type for context keys to avoid collisions
type contextKey string

// Context keys and constants used throughout the middleware chain
const (
	// JWTDataKey is the context key for storing JWT data
	JWTDataKey contextKey = "jwtData"
	// CurrentUserKey is the context key for storing current user information
	CurrentUserKey contextKey = "currentUser"
	// AuthResultKey is the context key for storing authorization results
	AuthResultKey contextKey = "authResult"
	// HTTP header and content type constants
	contentTypeHeader = "Content-Type"
	contentTypeJSON   = "application/json"
	// Error message constants
	errEncodeResponse = "failed to encode error response"
	errInvalidToken   = "Unauthorized: Invalid token"
)

// traceIDCtxKey is a custom type for trace ID context key
type traceIDCtxKey struct{}

// traceIDKey is the context key for storing trace IDs
var traceIDKey traceIDCtxKey

// Helper functions for middleware operations

// generateTraceID creates a new OpenTelemetry-compatible trace ID for request tracking.
// It generates 16 random bytes and converts them to a hex-encoded trace ID string.
// Returns an empty trace ID string if generation fails.
func generateTraceID() string {
	var traceID trace.TraceID
	// Generate 16 random bytes for trace ID
	randBytes := make([]byte, 16)

	_, err := rand.Read(randBytes)
	if err != nil {
		slog.Error("Failed to generate random bytes", "error", err)
		return traceID.String()
	}

	// Convert random bytes to hex string
	hexString := hex.EncodeToString(randBytes)

	// Create OpenTelemetry TraceID from hex string
	traceID, err = trace.TraceIDFromHex(hexString)
	if err != nil {
		slog.Error("Failed to create TraceID from hex", "error", err)
		return traceID.String()
	}

	return traceID.String()
}

// getTraceID retrieves the trace ID from the request context.
// If no trace ID exists in the context, it generates a new one.
func getTraceID(ctx context.Context) string {
	val, ok := ctx.Value(traceIDKey).(string)
	if !ok {
		return generateTraceID()
	}

	return val
}

// isServerError checks if the HTTP status code indicates a server error (5xx).
func isServerError(code int) bool {
	return code >= 500 && code <= 599
}

// isClientError checks if the HTTP status code indicates a client error (4xx),
// excluding 404 Not Found which is handled separately.
func isClientError(code int) bool {
	return code >= 400 && code <= 499 && code != http.StatusNotFound
}

// NotFound components
type notFoundResponseWriter struct {
	http.ResponseWriter
	statusCode int
	written    bool
}

func (w *notFoundResponseWriter) Write(bytes []byte) (int, error) {
	if w.statusCode == http.StatusNotFound {
		w.written = true
		return len(bytes), nil // Suppress the default 404 message
	}

	return w.ResponseWriter.Write(bytes)
}

func (w *notFoundResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	if statusCode != http.StatusNotFound {
		w.ResponseWriter.WriteHeader(statusCode)
	}
}

func NotFoundMiddleware() httpsvr.Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			nfw := &notFoundResponseWriter{ResponseWriter: w}
			next.ServeHTTP(nfw, r)

			if nfw.statusCode == http.StatusNotFound {
				w.Header().Set(contentTypeHeader, contentTypeJSON)
				w.WriteHeader(http.StatusNotFound)

				err := json.NewEncoder(w).Encode(httpsvr.ErrorResponse{
					Errors: []httpsvr.ErrorObject{
						{
							ID:     getTraceID(r.Context()),
							Status: "404",
							Title:  "Route not found",
							Detail: "The requested route was not found",
							Source: &httpsvr.ErrorSource{
								Pointer: r.URL.Path,
							},
						},
					},
				})
				if err != nil {
					slog.Error(errEncodeResponse, "error", err)
				}
			}
		})
	}
}

// Internal Server Error components
type internalServerResponseWriter struct {
	http.ResponseWriter
	statusCode int
	written    bool
}

func (w *internalServerResponseWriter) Write(bytes []byte) (int, error) {
	if isServerError(w.statusCode) {
		w.written = true
		return len(bytes), nil
	}

	return w.ResponseWriter.Write(bytes)
}

func (w *internalServerResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	if !isServerError(statusCode) {
		w.ResponseWriter.WriteHeader(statusCode)
	}
}

func InternalServerErrorMiddleware() httpsvr.Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			isw := &internalServerResponseWriter{ResponseWriter: w}
			next.ServeHTTP(isw, r)

			if isServerError(isw.statusCode) {
				w.Header().Set(contentTypeHeader, contentTypeJSON)
				w.WriteHeader(isw.statusCode)

				err := json.NewEncoder(w).Encode(httpsvr.ErrorResponse{
					Errors: []httpsvr.ErrorObject{
						{
							ID:     getTraceID(r.Context()),
							Status: strconv.Itoa(isw.statusCode),
							Title:  "Internal Server Error",
							Detail: http.StatusText(isw.statusCode),
							Source: &httpsvr.ErrorSource{
								Pointer: r.URL.Path,
							},
						},
					},
				})
				if err != nil {
					slog.Error(errEncodeResponse, "error", err)
				}
			}
		})
	}
}

// Client Error components
type clientErrorResponseWriter struct {
	http.ResponseWriter
	body       *bytes.Buffer
	statusCode int
	written    bool
}

func (w *clientErrorResponseWriter) Write(bytes []byte) (int, error) {
	if isClientError(w.statusCode) {
		w.written = true
		return w.body.Write(bytes)
	}

	return w.ResponseWriter.Write(bytes)
}

func (w *clientErrorResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	if !isClientError(statusCode) {
		w.ResponseWriter.WriteHeader(statusCode)
	}
}

func ClientErrorMiddleware() httpsvr.Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			crw := &clientErrorResponseWriter{
				ResponseWriter: w,
				body:           new(bytes.Buffer),
			}
			next.ServeHTTP(crw, r)

			if isClientError(crw.statusCode) {
				w.Header().Set(contentTypeHeader, contentTypeJSON)
				w.WriteHeader(crw.statusCode)

				err := json.NewEncoder(w).Encode(httpsvr.ErrorResponse{
					Errors: []httpsvr.ErrorObject{
						{
							ID:     getTraceID(r.Context()),
							Status: strconv.Itoa(crw.statusCode),
							Title:  http.StatusText(crw.statusCode),
							Detail: strings.TrimSpace(crw.body.String()),
							Source: &httpsvr.ErrorSource{
								Pointer: r.URL.Path,
							},
						},
					},
				})
				if err != nil {
					slog.Error(errEncodeResponse, "error", err)
				}
			}
		})
	}
}

func TracingMiddleware() httpsvr.Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			traceID := generateTraceID()
			ctx := context.WithValue(r.Context(), traceIDKey, traceID)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

func AuthMiddleware(cognitoSigner string) httpsvr.Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Extract the Cognito JWT token from the X-Amzn-Oidc-Data header
			cognitoToken := strings.TrimSpace(r.Header.Get("X-Amzn-Oidc-Data"))

			if cognitoToken == "" {
				http.Error(w, "Unauthorized: Missing Cognito token", http.StatusUnauthorized)
				return
			}

			// You can now use the cognitoToken for further processing or validation
			input := map[string]any{
				"signer": cognitoSigner,
				"jwt":    cognitoToken, // Add the token to the input map if needed
			}

			jsonData, err := policies.EvalCognitoPolicy(r.Context(), input)
			if err != nil {
				http.Error(w, "Unauthorized: Invalid token, "+err.Error(), http.StatusUnauthorized)
				return
			}

			ctx := context.WithValue(r.Context(), JWTDataKey, jsonData)
			r = r.WithContext(ctx)

			next.ServeHTTP(w, r)
		})
	}
}

func UserProvisioningMiddleware(i *do.Injector) httpsvr.Middleware {
	userProvisioningInteractor := do.MustInvoke[*in.UserProvisioningInteractor](i)

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			jwtData, ok := r.Context().Value(JWTDataKey).(policies.JwtData)
			if !ok {
				http.Error(w, errInvalidToken, http.StatusUnauthorized)

				return
			}

			user, err := userProvisioningInteractor.GetOrCreateUserFromJWT(r.Context(), jwtData)
			if err != nil {
				slog.Error("Failed to get or create user from JWT", "error", err)
				http.Error(w, errInvalidToken, http.StatusUnauthorized)

				return
			}

			currentUser := out.CurrentUserViewModel{
				ID:        user.ID,
				Email:     user.Email,
				FirstName: user.FirstName,
				LastName:  user.LastName,
				Role:      user.Role,
			}

			ctx := context.WithValue(r.Context(), CurrentUserKey, currentUser)
			r = r.WithContext(ctx)

			next.ServeHTTP(w, r)
		})
	}
}

func UserAuthorizationMiddleware(i *do.Injector) httpsvr.Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			currentUser, ok := r.Context().Value(CurrentUserKey).(out.CurrentUserViewModel)
			if !ok {
				http.Error(w, "Unauthorized: Invalid token", http.StatusUnauthorized)

				return
			}

			action := "read"
			if r.Method != http.MethodGet {
				action = "write"
			}

			if r.Method == http.MethodDelete {
				action = "delete"
			}

			rbac := do.MustInvoke[*policies.RBACPolicy](i)
			authRequest := &policies.AuthRequest{
				TenantID:      "default",
				Service:       "ea-service",
				Path:          r.URL.Path,
				Actions:       []string{action},
				AllowedScopes: []string{},
				User: policies.User{
					ID:   currentUser.ID.String(),
					Role: currentUser.Role,
				},
			}

			result, err := rbac.Validate(authRequest, policies.ValidateOptions{
				DisableScopesValidation: true,
			})
			if err != nil {
				slog.Info("Auth result", "result", result, "error", err, "authRequest", authRequest, "currentUser", currentUser)
				http.Error(w, "Unauthorized: Access denied", http.StatusUnauthorized)

				return
			}

			ctx := context.WithValue(r.Context(), AuthResultKey, result)
			r = r.WithContext(ctx)

			next.ServeHTTP(w, r)
		})
	}
}

// SkipMiddlewaresForPaths creates a middleware that will skip all provided middlewares
// if the request URL path matches any of the patterns in skipURLPatterns.
func SkipMiddlewaresForPaths(skipURLPatterns []string, middlewares ...httpsvr.Middleware) httpsvr.Middleware {
	return func(next http.Handler) http.Handler {
		// First, apply all middlewares in reverse order to build the middleware chain
		handler := next
		for i := len(middlewares) - 1; i >= 0; i-- {
			handler = middlewares[i](handler)
		}

		// Then return a handler that either skips all middlewares or applies them
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Check if the current path matches any of the skip patterns
			for _, pattern := range skipURLPatterns {
				matched, err := regexp.MatchString(pattern, r.URL.Path)
				if err == nil && matched {
					// If matched, skip all middlewares and go straight to the final handler
					next.ServeHTTP(w, r)
					return
				}
			}

			// If no match, use the middleware chain
			handler.ServeHTTP(w, r)
		})
	}
}
