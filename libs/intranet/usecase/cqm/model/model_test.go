package model

import (
	"testing"
)

func TestSonarAnalysisStatusIsValid(t *testing.T) {
	tests := []struct {
		name   string
		status SonarAnalysisStatus
		want   bool
	}{
		{
			name:   "valid_processing_status",
			status: SonarAnalysisProcessing,
			want:   true,
		},
		{
			name:   "valid_succeeded_status",
			status: SonarAnalysisSucceeded,
			want:   true,
		},
		{
			name:   "valid_failed_status",
			status: SonarAnalysisFailed,
			want:   true,
		},
		{
			name:   "invalid_empty_status",
			status: SonarAnalysisStatus(""),
			want:   false,
		},
		{
			name:   "invalid_unknown_status",
			status: SonarAnalysisStatus("unknown"),
			want:   false,
		},
		{
			name:   "invalid_random_string",
			status: SonarAnalysisStatus("random-status"),
			want:   false,
		},
		{
			name:   "invalid_case_sensitive",
			status: SonarAnalysisStatus("PROCESSING"),
			want:   false,
		},
		{
			name:   "invalid_partial_match",
			status: SonarAnalysisStatus("process"),
			want:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.status.IsValid()
			if got != tt.want {
				t.<PERSON>("SonarAnalysisStatus.IsValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSonarAnalysisStatusString(t *testing.T) {
	tests := []struct {
		name   string
		status SonarAnalysisStatus
		want   string
	}{
		{
			name:   "processing_status_string",
			status: SonarAnalysisProcessing,
			want:   "processing",
		},
		{
			name:   "succeeded_status_string",
			status: SonarAnalysisSucceeded,
			want:   "succeeded",
		},
		{
			name:   "failed_status_string",
			status: SonarAnalysisFailed,
			want:   "failed",
		},
		{
			name:   "empty_status_string",
			status: SonarAnalysisStatus(""),
			want:   "",
		},
		{
			name:   "custom_status_string",
			status: SonarAnalysisStatus("custom-status"),
			want:   "custom-status",
		},
		{
			name:   "unicode_status_string",
			status: SonarAnalysisStatus("状态"),
			want:   "状态",
		},
		{
			name:   "special_chars_status_string",
			status: SonarAnalysisStatus("status-with-special!@#$%^&*()"),
			want:   "status-with-special!@#$%^&*()",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.status.String()
			if got != tt.want {
				t.Errorf("SonarAnalysisStatus.String() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSonarAnalysisStatusValidate(t *testing.T) {
	tests := []struct {
		name    string
		status  SonarAnalysisStatus
		wantErr bool
		errType error
	}{
		{
			name:    "valid_processing_status",
			status:  SonarAnalysisProcessing,
			wantErr: false,
		},
		{
			name:    "valid_succeeded_status",
			status:  SonarAnalysisSucceeded,
			wantErr: false,
		},
		{
			name:    "valid_failed_status",
			status:  SonarAnalysisFailed,
			wantErr: false,
		},
		{
			name:    "invalid_empty_status",
			status:  SonarAnalysisStatus(""),
			wantErr: true,
			errType: ErrInvalidSonarAnalysisStatus,
		},
		{
			name:    "invalid_unknown_status",
			status:  SonarAnalysisStatus("unknown"),
			wantErr: true,
			errType: ErrInvalidSonarAnalysisStatus,
		},
		{
			name:    "invalid_random_string",
			status:  SonarAnalysisStatus("random-status"),
			wantErr: true,
			errType: ErrInvalidSonarAnalysisStatus,
		},
		{
			name:    "invalid_case_sensitive",
			status:  SonarAnalysisStatus("PROCESSING"),
			wantErr: true,
			errType: ErrInvalidSonarAnalysisStatus,
		},
		{
			name:    "invalid_partial_match",
			status:  SonarAnalysisStatus("process"),
			wantErr: true,
			errType: ErrInvalidSonarAnalysisStatus,
		},
		{
			name:    "invalid_whitespace_status",
			status:  SonarAnalysisStatus(" processing "),
			wantErr: true,
			errType: ErrInvalidSonarAnalysisStatus,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.status.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("SonarAnalysisStatus.Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && tt.errType != nil {
				if err != tt.errType {
					t.Errorf("SonarAnalysisStatus.Validate() error = %v, want %v", err, tt.errType)
				}
			}
		})
	}
}

func TestSonarAnalysisStatusConstants(t *testing.T) {
	// Test that constants have expected values
	tests := []struct {
		name     string
		constant SonarAnalysisStatus
		expected string
	}{
		{
			name:     "processing_constant",
			constant: SonarAnalysisProcessing,
			expected: "processing",
		},
		{
			name:     "succeeded_constant",
			constant: SonarAnalysisSucceeded,
			expected: "succeeded",
		},
		{
			name:     "failed_constant",
			constant: SonarAnalysisFailed,
			expected: "failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if string(tt.constant) != tt.expected {
				t.Errorf("constant %s = %v, want %v", tt.name, string(tt.constant), tt.expected)
			}
		})
	}
}

func TestSonarAnalysisStatusEdgeCases(t *testing.T) {
	tests := []struct {
		name   string
		status SonarAnalysisStatus
		verify func(t *testing.T, status SonarAnalysisStatus)
	}{
		{
			name:   "nil_like_behavior",
			status: SonarAnalysisStatus(""),
			verify: func(t *testing.T, status SonarAnalysisStatus) {
				if status.IsValid() {
					t.Error("empty status should not be valid")
				}
				if status.String() != "" {
					t.Errorf("empty status string should be empty, got %s", status.String())
				}
				if err := status.Validate(); err == nil {
					t.Error("empty status validation should fail")
				}
			},
		},
		{
			name:   "consistency_check",
			status: SonarAnalysisProcessing,
			verify: func(t *testing.T, status SonarAnalysisStatus) {
				// All valid statuses should pass all checks
				if !status.IsValid() {
					t.Error("valid status should pass IsValid()")
				}
				if status.String() != string(status) {
					t.Error("String() should return the underlying string value")
				}
				if err := status.Validate(); err != nil {
					t.Errorf("valid status should pass Validate(), got error: %v", err)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.verify != nil {
				tt.verify(t, tt.status)
			}
		})
	}
}
