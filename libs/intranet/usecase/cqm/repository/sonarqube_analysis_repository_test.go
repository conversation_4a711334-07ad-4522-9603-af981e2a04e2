package repository

import (
	"context"
	"testing"
	"time"

	"sa-intranet/core/repository"
	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
)

func TestNewSonarqubeAnalysisDefaultRepository(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	tests := []struct {
		name    string
		setup   func() *do.Injector
		wantErr bool
	}{
		{
			name: "success_with_valid_injector",
			setup: func() *do.Injector {
				return injector
			},
			wantErr: false,
		},
		{
			name: "error_when_db_not_available",
			setup: func() *do.Injector {
				emptyInjector := do.New()
				return emptyInjector
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewSonarqubeAnalysisDefaultRepository(tt.setup())
			if (err != nil) != tt.wantErr {
				t.Errorf("NewSonarqubeAnalysisDefaultRepository() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && got == nil {
				t.Error("NewSonarqubeAnalysisDefaultRepository() returned nil repository")
			}
		})
	}
}

func TestSonarqubeAnalysisDefaultRepository_Find(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeAnalysisDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name    string
		id      uuid.UUID
		wantErr bool
		verify  func(t *testing.T, analysis *model.SonarqubeAnalysis)
	}{
		{
			name:    "error_find_nonexistent_analysis",
			id:      uuid.New(),
			wantErr: true,
		},
		{
			name:    "error_find_nil_uuid",
			id:      uuid.Nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.Find(ctx, tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("Find() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestSonarqubeAnalysisDefaultRepository_List(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeAnalysisDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name    string
		params  repository.PaginationParams[SonarqubeAnalysisFilter]
		wantErr bool
		verify  func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeAnalysis])
	}{
		{
			name: "success_list_all_analyses",
			params: repository.PaginationParams[SonarqubeAnalysisFilter]{
				Page:     1,
				PageSize: 10,
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeAnalysis]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				// Analyses might be empty if no fixtures exist
				if result.TotalItems < 0 {
					t.Error("expected non-negative total items")
				}
			},
		},
		{
			name: "success_filter_by_analysis_id",
			params: repository.PaginationParams[SonarqubeAnalysisFilter]{
				Page:     1,
				PageSize: 10,
				Filters: SonarqubeAnalysisFilter{
					AnalysisID: "test-analysis-123",
				},
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeAnalysis]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				// Should return empty result for non-existent analysis ID
				if result.TotalItems != 0 {
					t.Errorf("expected 0 analyses for non-existent ID, got %d", result.TotalItems)
				}
			},
		},
		{
			name: "error_invalid_page_parameters",
			params: repository.PaginationParams[SonarqubeAnalysisFilter]{
				Page:     0, // Invalid page
				PageSize: 10,
			},
			wantErr: true,
		},
		{
			name: "error_invalid_page_size",
			params: repository.PaginationParams[SonarqubeAnalysisFilter]{
				Page:     1,
				PageSize: 0, // Invalid page size
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.List(ctx, tt.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("List() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestSonarqubeAnalysisDefaultRepository_Save(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeAnalysisDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	// Create a test SonarqubeProject first
	sonarRepo, err := NewSonarqubeProjectDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create sonar project repository: %v", err)
	}

	// Get a valid JiraProject ID from fixtures
	fixtureJiraProjectID := uuid.MustParse("0195dda5-2a22-7659-8648-289e7d9f522d")

	testProject := &model.SonarqubeProject{
		ProjectName:   "Test Analysis Project",
		ProjectKey:    "test.analysis.project",
		Branch:        "main",
		Active:        true,
		JiraProjectID: fixtureJiraProjectID,
	}

	savedProject, err := sonarRepo.Save(ctx, testProject, false)
	if err != nil {
		t.Fatalf("Failed to create test project: %v", err)
	}

	tests := []struct {
		name     string
		analysis *model.SonarqubeAnalysis
		forceID  bool
		wantErr  bool
		verify   func(t *testing.T, analysisID uuid.UUID)
	}{
		{
			name: "success_save_new_analysis",
			analysis: &model.SonarqubeAnalysis{
				SonarqubeProjectID: savedProject.ID,
				AnalysisID:         "test-analysis-123",
				WebhookPayload:     map[string]interface{}{"test": "payload"},
				Measures:           map[string]interface{}{"coverage": "80.5"},
				Score:              85.5,
				Status:             model.SonarAnalysisSucceeded,
				AnalysedAt:         time.Now(),
				QualityGateName:    "Sonar way",
				QualityGateStatus:  "OK",
				ErrorDetails:       map[string]interface{}{},
				IssuesTotal:        5,
				IssuesEffortTotal:  30,
			},
			forceID: false,
			wantErr: false,
			verify: func(t *testing.T, analysisID uuid.UUID) {
				if analysisID == uuid.Nil {
					t.Error("expected generated ID, got nil UUID")
				}
			},
		},
		{
			name: "success_save_with_force_id",
			analysis: &model.SonarqubeAnalysis{
				ID:                 uuid.New(),
				SonarqubeProjectID: savedProject.ID,
				AnalysisID:         "forced-analysis-456",
				WebhookPayload:     map[string]interface{}{"forced": "payload"},
				Status:             model.SonarAnalysisProcessing,
				AnalysedAt:         time.Now(),
			},
			forceID: true,
			wantErr: false,
			verify: func(t *testing.T, analysisID uuid.UUID) {
				if analysisID == uuid.Nil {
					t.Error("expected ID to be preserved, got nil UUID")
				}
			},
		},
		{
			name:     "error_save_nil_analysis",
			analysis: nil,
			forceID:  false,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := repo.Save(ctx, tt.analysis, tt.forceID)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil && tt.analysis != nil {
				tt.verify(t, tt.analysis.ID)
			}
		})
	}
}
