package repository

import (
	"context"
	"testing"

	"sa-intranet/core/repository"
	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
)

func TestNewCompanyClientDefaultRepository(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	tests := []struct {
		name    string
		setup   func() *do.Injector
		wantErr bool
	}{
		{
			name: "success_with_valid_injector",
			setup: func() *do.Injector {
				return injector
			},
			wantErr: false,
		},
		{
			name: "error_when_db_not_available",
			setup: func() *do.Injector {
				emptyInjector := do.New()
				return emptyInjector
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewCompanyClientDefaultRepository(tt.setup())
			if (err != nil) != tt.wantErr {
				t.Errorf("NewCompanyClientDefaultRepository() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && got == nil {
				t.Error("NewCompanyClientDefaultRepository() returned nil repository")
			}
		})
	}
}

func TestCompanyClientDefaultRepository_Find(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewCompanyClientDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name    string
		id      uuid.UUID
		wantErr bool
		verify  func(t *testing.T, client *model.CompanyClient)
	}{
		{
			name:    "error_find_nonexistent_client",
			id:      uuid.New(),
			wantErr: true,
		},
		{
			name:    "error_find_nil_uuid",
			id:      uuid.Nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.Find(ctx, tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("Find() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestCompanyClientDefaultRepository_List(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewCompanyClientDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name   string
		params repository.PaginationParams[CompanyClientFilter]
		verify func(t *testing.T, result *repository.PaginatedResult[model.CompanyClient])
	}{
		{
			name: "success_list_all_clients",
			params: repository.PaginationParams[CompanyClientFilter]{
				Page:     1,
				PageSize: 10,
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.CompanyClient]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				// Clients might be empty if no fixtures exist
				if result.TotalItems < 0 {
					t.Error("expected non-negative total items")
				}
			},
		},
		{
			name: "success_filter_by_name",
			params: repository.PaginationParams[CompanyClientFilter]{
				Page:     1,
				PageSize: 10,
				Filters: CompanyClientFilter{
					Name: "Test",
				},
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.CompanyClient]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				// Should return empty result for non-existent name
				if result.TotalItems != 0 {
					t.Errorf("expected 0 clients for non-existent name, got %d", result.TotalItems)
				}
			},
		},
		{
			name: "success_filter_no_matches",
			params: repository.PaginationParams[CompanyClientFilter]{
				Page:     1,
				PageSize: 10,
				Filters: CompanyClientFilter{
					Name: "NonExistentClient",
				},
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.CompanyClient]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				if result.TotalItems != 0 {
					t.Errorf("expected 0 clients, got %d", result.TotalItems)
				}
				if len(result.Items) != 0 {
					t.Errorf("expected 0 items, got %d", len(result.Items))
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.List(ctx, tt.params)
			if err != nil {
				t.Errorf("List() error = %v", err)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestCompanyClientDefaultRepository_Save(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewCompanyClientDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name   string
		client *model.CompanyClient
		wantErr bool
		verify func(t *testing.T, saved *model.CompanyClient)
	}{
		{
			name: "success_save_new_client",
			client: &model.CompanyClient{
				Name: "Test Company Client",
			},
			wantErr: false,
			verify: func(t *testing.T, saved *model.CompanyClient) {
				if saved == nil {
					t.Error("expected saved client, got nil")
					return
				}
				if saved.ID == uuid.Nil {
					t.Error("expected generated ID, got nil UUID")
				}
				if saved.Name != "Test Company Client" {
					t.Errorf("expected Name 'Test Company Client', got %s", saved.Name)
				}
				if saved.CreatedAt.IsZero() {
					t.Error("expected CreatedAt to be set")
				}
				if saved.UpdatedAt.IsZero() {
					t.Error("expected UpdatedAt to be set")
				}
			},
		},
		{
			name: "success_update_existing_client",
			client: &model.CompanyClient{
				ID:   uuid.New(),
				Name: "Updated Company Client",
			},
			wantErr: false,
			verify: func(t *testing.T, saved *model.CompanyClient) {
				if saved == nil {
					t.Error("expected saved client, got nil")
					return
				}
				if saved.Name != "Updated Company Client" {
					t.Errorf("expected Name 'Updated Company Client', got %s", saved.Name)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.Save(ctx, tt.client)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}
