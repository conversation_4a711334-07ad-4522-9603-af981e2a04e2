package repository

import (
	"context"
	"testing"

	"sa-intranet/core/repository"
	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
)

func TestNewSonarqubeProjectDefaultRepository(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	tests := []struct {
		name    string
		setup   func() *do.Injector
		wantErr bool
	}{
		{
			name: "success_with_valid_injector",
			setup: func() *do.Injector {
				return injector
			},
			wantErr: false,
		},
		{
			name: "error_when_db_not_available",
			setup: func() *do.Injector {
				emptyInjector := do.New()
				return emptyInjector
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewSonarqubeProjectDefaultRepository(tt.setup())
			if (err != nil) != tt.wantErr {
				t.Errorf("NewSonarqubeProjectDefaultRepository() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && got == nil {
				t.Error("NewSonarqubeProjectDefaultRepository() returned nil repository")
			}
		})
	}
}

func TestSonarqubeProjectDefaultRepository_Find(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeProjectDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name    string
		id      uuid.UUID
		wantErr bool
		verify  func(t *testing.T, project *model.SonarqubeProject)
	}{
		{
			name:    "error_find_nonexistent_project",
			id:      uuid.New(),
			wantErr: true,
		},
		{
			name:    "error_find_nil_uuid",
			id:      uuid.Nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.Find(ctx, tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("Find() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestSonarqubeProjectDefaultRepository_FindByJiraProjectID(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeProjectDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	// Test with fixture data
	fixtureJiraProjectID := uuid.MustParse("0195dda5-2a22-7659-8648-289e7d9f522d")

	tests := []struct {
		name          string
		jiraProjectID uuid.UUID
		wantErr       bool
		verify        func(t *testing.T, projects []*model.SonarqubeProject)
	}{
		{
			name:          "success_find_by_existing_jira_project_id",
			jiraProjectID: fixtureJiraProjectID,
			wantErr:       false,
			verify: func(t *testing.T, projects []*model.SonarqubeProject) {
				if projects == nil {
					t.Error("expected projects slice, got nil")
					return
				}
				if len(projects) < 1 {
					t.Error("expected at least 1 project from fixtures")
				}
				for _, project := range projects {
					if project.JiraProjectID != fixtureJiraProjectID {
						t.Errorf("expected JiraProjectID %v, got %v", fixtureJiraProjectID, project.JiraProjectID)
					}
				}
			},
		},
		{
			name:          "success_find_by_nonexistent_jira_project_id",
			jiraProjectID: uuid.New(),
			wantErr:       false,
			verify: func(t *testing.T, projects []*model.SonarqubeProject) {
				if projects == nil {
					t.Error("expected projects slice, got nil")
					return
				}
				if len(projects) != 0 {
					t.Errorf("expected 0 projects, got %d", len(projects))
				}
			},
		},
		{
			name:          "success_find_by_nil_uuid",
			jiraProjectID: uuid.Nil,
			wantErr:       false,
			verify: func(t *testing.T, projects []*model.SonarqubeProject) {
				if projects == nil {
					t.Error("expected projects slice, got nil")
					return
				}
				if len(projects) != 0 {
					t.Errorf("expected 0 projects for nil UUID, got %d", len(projects))
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.FindByJiraProjectID(ctx, tt.jiraProjectID)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindByJiraProjectID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestSonarqubeProjectDefaultRepository_List(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeProjectDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name   string
		params repository.PaginationParams[SonarqubeProjectFilter]
		verify func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeProject])
	}{
		{
			name: "success_list_all_projects",
			params: repository.PaginationParams[SonarqubeProjectFilter]{
				Page:     1,
				PageSize: 10,
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeProject]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				if result.TotalItems < 1 {
					t.Error("expected at least 1 project from fixtures")
				}
				if len(result.Items) == 0 {
					t.Error("expected at least 1 project in items")
				}
			},
		},
		{
			name: "success_filter_by_name",
			params: repository.PaginationParams[SonarqubeProjectFilter]{
				Page:     1,
				PageSize: 10,
				Filters: SonarqubeProjectFilter{
					Name: "Example",
				},
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeProject]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				if result.TotalItems < 1 {
					t.Error("expected at least 1 project matching 'Example'")
				}
			},
		},
		{
			name: "success_filter_by_project_key",
			params: repository.PaginationParams[SonarqubeProjectFilter]{
				Page:     1,
				PageSize: 10,
				Filters: SonarqubeProjectFilter{
					ProjectKey: "example.project-backend",
				},
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeProject]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				if result.TotalItems < 1 {
					t.Error("expected at least 1 project matching project key")
				}
			},
		},
		{
			name: "success_filter_no_matches",
			params: repository.PaginationParams[SonarqubeProjectFilter]{
				Page:     1,
				PageSize: 10,
				Filters: SonarqubeProjectFilter{
					Name: "NonExistentProject",
				},
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeProject]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				if result.TotalItems != 0 {
					t.Errorf("expected 0 projects, got %d", result.TotalItems)
				}
				if len(result.Items) != 0 {
					t.Errorf("expected 0 items, got %d", len(result.Items))
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.List(ctx, tt.params)
			if err != nil {
				t.Errorf("List() error = %v", err)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestSonarqubeProjectDefaultRepository_Save(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeProjectDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	// Get a valid JiraProject ID from fixtures
	fixtureJiraProjectID := uuid.MustParse("0195dda5-2a22-7659-8648-289e7d9f522d")

	tests := []struct {
		name    string
		project *model.SonarqubeProject
		forceID bool
		wantErr bool
		verify  func(t *testing.T, saved *model.SonarqubeProject)
	}{
		{
			name: "success_save_new_project",
			project: &model.SonarqubeProject{
				ProjectName:   "Test Sonar Project",
				ProjectKey:    "test.sonar.project",
				Branch:        "main",
				Active:        true,
				JiraProjectID: fixtureJiraProjectID,
			},
			forceID: false,
			wantErr: false,
			verify: func(t *testing.T, saved *model.SonarqubeProject) {
				if saved == nil {
					t.Error("expected saved project, got nil")
					return
				}
				if saved.ID == uuid.Nil {
					t.Error("expected generated ID, got nil UUID")
				}
				if saved.ProjectName != "Test Sonar Project" {
					t.Errorf("expected ProjectName 'Test Sonar Project', got %s", saved.ProjectName)
				}
				if saved.ProjectKey != "test.sonar.project" {
					t.Errorf("expected ProjectKey 'test.sonar.project', got %s", saved.ProjectKey)
				}
				if saved.Branch != "main" {
					t.Errorf("expected Branch 'main', got %s", saved.Branch)
				}
				if saved.CreatedAt.IsZero() {
					t.Error("expected CreatedAt to be set")
				}
				if saved.UpdatedAt.IsZero() {
					t.Error("expected UpdatedAt to be set")
				}
			},
		},
		{
			name: "success_save_with_force_id",
			project: &model.SonarqubeProject{
				ID:            uuid.New(),
				ProjectName:   "Forced ID Project",
				ProjectKey:    "forced.id.project",
				Branch:        "develop",
				Active:        true,
				JiraProjectID: fixtureJiraProjectID,
			},
			forceID: true,
			wantErr: false,
			verify: func(t *testing.T, saved *model.SonarqubeProject) {
				if saved == nil {
					t.Error("expected saved project, got nil")
					return
				}
				if saved.ProjectName != "Forced ID Project" {
					t.Errorf("expected ProjectName 'Forced ID Project', got %s", saved.ProjectName)
				}
				if saved.Branch != "develop" {
					t.Errorf("expected Branch 'develop', got %s", saved.Branch)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.Save(ctx, tt.project, tt.forceID)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}
