package repository

import (
	"context"
	"testing"

	"sa-intranet/core/repository"
	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
)

func TestNewSonarqubeIssueDefaultRepository(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	tests := []struct {
		name    string
		setup   func() *do.Injector
		wantErr bool
	}{
		{
			name: "success_with_valid_injector",
			setup: func() *do.Injector {
				return injector
			},
			wantErr: false,
		},
		{
			name: "error_when_db_not_available",
			setup: func() *do.Injector {
				emptyInjector := do.New()
				return emptyInjector
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewSonarqubeIssueDefaultRepository(tt.setup())
			if (err != nil) != tt.wantErr {
				t.Errorf("NewSonarqubeIssueDefaultRepository() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && got == nil {
				t.Error("NewSonarqubeIssueDefaultRepository() returned nil repository")
			}
		})
	}
}

func TestSonarqubeIssueDefaultRepository_Find(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeIssueDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name    string
		id      uuid.UUID
		wantErr bool
		verify  func(t *testing.T, issue *model.SonarqubeIssue)
	}{
		{
			name:    "error_find_nonexistent_issue",
			id:      uuid.New(),
			wantErr: true,
		},
		{
			name:    "error_find_nil_uuid",
			id:      uuid.Nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.Find(ctx, tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("Find() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestSonarqubeIssueDefaultRepository_FindByIssueID(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeIssueDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name    string
		issueID string
		wantErr bool
		verify  func(t *testing.T, issue *model.SonarqubeIssue)
	}{
		{
			name:    "error_find_nonexistent_issue_id",
			issueID: "nonexistent-issue-id",
			wantErr: true,
		},
		{
			name:    "error_find_empty_issue_id",
			issueID: "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.FindByIssueID(ctx, tt.issueID)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindByIssueID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestSonarqubeIssueDefaultRepository_List(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeIssueDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name   string
		params repository.PaginationParams[SonarqubeIssueFilter]
		verify func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeIssue])
	}{
		{
			name: "success_list_all_issues",
			params: repository.PaginationParams[SonarqubeIssueFilter]{
				Page:     1,
				PageSize: 10,
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeIssue]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				// Issues might be empty if no fixtures exist
				if result.TotalItems < 0 {
					t.Error("expected non-negative total items")
				}
			},
		},
		{
			name: "success_filter_by_issue_ids",
			params: repository.PaginationParams[SonarqubeIssueFilter]{
				Page:     1,
				PageSize: 10,
				Filters: SonarqubeIssueFilter{
					IssueIDs: []string{"test-issue-1", "test-issue-2"},
				},
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.SonarqubeIssue]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				// Should return empty result for non-existent issue IDs
				if result.TotalItems != 0 {
					t.Errorf("expected 0 issues for non-existent IDs, got %d", result.TotalItems)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.List(ctx, tt.params)
			if err != nil {
				t.Errorf("List() error = %v", err)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestSonarqubeIssueDefaultRepository_Save(t *testing.T) {
	ctx := context.Background()
	injector := do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx),
		testhelpers.WithFixtures(ctx),
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	repo, err := NewSonarqubeIssueDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name    string
		issue   *model.SonarqubeIssue
		wantErr bool
		verify  func(t *testing.T, issueID uuid.UUID)
	}{
		{
			name: "success_save_new_issue",
			issue: &model.SonarqubeIssue{
				IssueID:     "test-issue-123",
				ProjectKey:  "test.project",
				Component:   "test.component",
				Rule:        "test:rule",
				Severity:    "MAJOR",
				Type:        "BUG",
				Status:      "OPEN",
				Message:     "Test issue message",
				Author:      "<EMAIL>",
				Line:        42,
				Hash:        "testhash123",
				Effort:      "5min",
				Debt:        "10min",
				Tags:        []string{"test", "bug"},
				Assignee:    "<EMAIL>",
				Resolution:  "",
				TextRange:   map[string]interface{}{"startLine": 42, "endLine": 42},
				Flows:       []map[string]interface{}{},
			},
			wantErr: false,
			verify: func(t *testing.T, issueID uuid.UUID) {
				if issueID == uuid.Nil {
					t.Error("expected generated ID, got nil UUID")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := repo.Save(ctx, tt.issue)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, tt.issue.ID)
			}
		})
	}
}
