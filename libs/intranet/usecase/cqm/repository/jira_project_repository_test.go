package repository_test

import (
	"context"
	"testing"
	"time"

	"sa-intranet/core/repository"
	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/cqm/model"
	cqmRepository "sa-intranet/usecase/cqm/repository"

	"github.com/google/uuid"
	"github.com/samber/do"
)

var injector *do.Injector

func TestMain(m *testing.M) {
	ctx := context.Background()
	injector = do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx), // Apply migrations, needs to be after WithPostgres
		testhelpers.WithFixtures(ctx),   // Load fixtures, needs to be after WithMigrations
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	// Run all tests
	m.Run()
}

func TestNewJiraProjectDefaultRepository(t *testing.T) {
	tests := []struct {
		name    string
		setup   func() *do.Injector
		wantErr bool
	}{
		{
			name: "success_with_valid_injector",
			setup: func() *do.Injector {
				return injector
			},
			wantErr: false,
		},
		{
			name: "error_when_db_not_available",
			setup: func() *do.Injector {
				emptyInjector := do.New()
				return emptyInjector
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := cqmRepository.NewJiraProjectDefaultRepository(tt.setup())
			if (err != nil) != tt.wantErr {
				t.Errorf("NewJiraProjectDefaultRepository() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && got == nil {
				t.Error("NewJiraProjectDefaultRepository() returned nil repository")
			}
		})
	}
}

func TestJiraProjectDefaultRepository_Find(t *testing.T) {
	ctx := context.Background()
	repo, err := NewJiraProjectDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	// Test with fixture data
	fixtureID := uuid.MustParse("0195dda5-2a22-7659-8648-289e7d9f522d")

	tests := []struct {
		name    string
		id      uuid.UUID
		wantErr bool
		verify  func(t *testing.T, project *model.JiraProject)
	}{
		{
			name:    "success_find_existing_project",
			id:      fixtureID,
			wantErr: false,
			verify: func(t *testing.T, project *model.JiraProject) {
				if project == nil {
					t.Error("expected project, got nil")
					return
				}
				if project.ID != fixtureID {
					t.Errorf("expected ID %v, got %v", fixtureID, project.ID)
				}
				if project.ProjectKey != "BPL" {
					t.Errorf("expected ProjectKey 'BPL', got %s", project.ProjectKey)
				}
				if project.Name != "Backend Practice Leads" {
					t.Errorf("expected Name 'Backend Practice Leads', got %s", project.Name)
				}
			},
		},
		{
			name:    "error_find_nonexistent_project",
			id:      uuid.New(),
			wantErr: true,
		},
		{
			name:    "error_find_nil_uuid",
			id:      uuid.Nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.Find(ctx, tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("Find() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestJiraProjectDefaultRepository_List(t *testing.T) {
	ctx := context.Background()
	repo, err := NewJiraProjectDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name   string
		params repository.PaginationParams[JiraProjectFilter]
		verify func(t *testing.T, result *repository.PaginatedResult[model.JiraProject])
	}{
		{
			name: "success_list_all_projects",
			params: repository.PaginationParams[JiraProjectFilter]{
				Page:     1,
				PageSize: 10,
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.JiraProject]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				if result.TotalItems < 1 {
					t.Error("expected at least 1 project from fixtures")
				}
				if len(result.Items) == 0 {
					t.Error("expected at least 1 project in items")
				}
			},
		},
		{
			name: "success_filter_by_name",
			params: repository.PaginationParams[JiraProjectFilter]{
				Page:     1,
				PageSize: 10,
				Filters: JiraProjectFilter{
					Name: "Backend",
				},
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.JiraProject]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				if result.TotalItems < 1 {
					t.Error("expected at least 1 project matching 'Backend'")
				}
			},
		},
		{
			name: "success_filter_no_matches",
			params: repository.PaginationParams[JiraProjectFilter]{
				Page:     1,
				PageSize: 10,
				Filters: JiraProjectFilter{
					Name: "NonExistentProject",
				},
			},
			verify: func(t *testing.T, result *repository.PaginatedResult[model.JiraProject]) {
				if result == nil {
					t.Error("expected result, got nil")
					return
				}
				if result.TotalItems != 0 {
					t.Errorf("expected 0 projects, got %d", result.TotalItems)
				}
				if len(result.Items) != 0 {
					t.Errorf("expected 0 items, got %d", len(result.Items))
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.List(ctx, tt.params)
			if err != nil {
				t.Errorf("List() error = %v", err)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}

func TestJiraProjectDefaultRepository_Save(t *testing.T) {
	ctx := context.Background()
	repo, err := NewJiraProjectDefaultRepository(injector)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}

	tests := []struct {
		name    string
		project *model.JiraProject
		wantErr bool
		verify  func(t *testing.T, saved *model.JiraProject)
	}{
		{
			name: "success_save_new_project",
			project: &model.JiraProject{
				JiraURL:    "https://test.atlassian.net",
				Username:   "testuser",
				Token:      "testtoken",
				Name:       "Test Project",
				ProjectID:  "10001",
				ProjectKey: "TEST",
				Active:     true,
			},
			wantErr: false,
			verify: func(t *testing.T, saved *model.JiraProject) {
				if saved == nil {
					t.Error("expected saved project, got nil")
					return
				}
				if saved.ID == uuid.Nil {
					t.Error("expected generated ID, got nil UUID")
				}
				if saved.Name != "Test Project" {
					t.Errorf("expected Name 'Test Project', got %s", saved.Name)
				}
				if saved.ProjectKey != "TEST" {
					t.Errorf("expected ProjectKey 'TEST', got %s", saved.ProjectKey)
				}
				if saved.CreatedAt.IsZero() {
					t.Error("expected CreatedAt to be set")
				}
				if saved.UpdatedAt.IsZero() {
					t.Error("expected UpdatedAt to be set")
				}
			},
		},
		{
			name: "success_update_existing_project",
			project: &model.JiraProject{
				ID:         uuid.MustParse("0195dda5-2a22-7659-8648-289e7d9f522d"),
				JiraURL:    "https://updated.atlassian.net",
				Username:   "updateduser",
				Token:      "updatedtoken",
				Name:       "Updated Project Name",
				ProjectID:  "10002",
				ProjectKey: "BPL",
				Active:     false,
				UpdatedAt:  time.Now(),
			},
			wantErr: false,
			verify: func(t *testing.T, saved *model.JiraProject) {
				if saved == nil {
					t.Error("expected saved project, got nil")
					return
				}
				if saved.Name != "Updated Project Name" {
					t.Errorf("expected Name 'Updated Project Name', got %s", saved.Name)
				}
				if saved.Active != false {
					t.Errorf("expected Active false, got %v", saved.Active)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := repo.Save(ctx, tt.project)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.verify != nil {
				tt.verify(t, got)
			}
		})
	}
}
