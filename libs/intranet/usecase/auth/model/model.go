// Package model defines domain entities for the authentication system.
// It contains the User model with database mappings and business logic hooks.
package model

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

// User represents a system user with authentication and profile information.
// It includes database mappings using Bun ORM tags and implements hooks for
// automatic ID generation and timestamp management.
type User struct {
	// BaseModel provides common Bun ORM functionality
	bun.BaseModel `bun:"table:users,alias:u"`

	// ID is the unique identifier for the user (UUID v7 for time-ordered IDs)
	ID uuid.UUID `bun:"id,pk,type:uuid,default:uuid_generate_v4()"`
	// Username is the unique username for authentication (required, unique)
	Username string `bun:"username,notnull,unique"`
	// FirstName is the user's first name (required)
	FirstName string `bun:"first_name,notnull"`
	// LastName is the user's last name (required)
	LastName string `bun:"last_name,notnull"`
	// Email is the user's email address (required, unique)
	Email string `bun:"email,notnull,unique"`
	// Role defines the user's role for authorization purposes (required)
	Role string `bun:"role,notnull"`

	// CreatedAt tracks when the user record was created
	CreatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
	// UpdatedAt tracks when the user record was last modified
	UpdatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
}

// Compile-time check to ensure User implements the BeforeAppendModelHook interface
var _ bun.BeforeAppendModelHook = (*User)(nil)

// BeforeAppendModel is a Bun ORM hook that executes before database operations.
// It automatically generates UUIDs for new records and updates timestamps for modifications.
// This ensures data consistency and proper audit trail maintenance.
//
// Parameters:
//   - ctx: Context for the database operation
//   - query: The database query being executed
//
// Returns an error if UUID generation fails during insert operations.
func (u *User) BeforeAppendModel(ctx context.Context, query bun.Query) error {
	switch query.(type) {
	case *bun.InsertQuery:
		// Generate a new UUID v7 for new user records if not already set
		if u.ID == uuid.Nil {
			id, err := uuid.NewV7()
			if err != nil {
				return err
			}

			u.ID = id
		}
	case *bun.UpdateQuery:
		// Automatically update the timestamp when modifying user records
		u.UpdatedAt = time.Now()
	}

	return nil
}
