// Package config provides configuration structures for the authentication system.
// It defines settings that control authentication behavior and default values.
package config

// AuthConfig holds configuration settings for the authentication system.
// These settings are loaded from environment variables and control various
// aspects of user authentication and authorization.
type AuthConfig struct {
	// DefaultRole specifies the default role assigned to new users when no role is explicitly provided.
	// This value is loaded from the APP_AUTH_DEFAULT_ROLE environment variable.
	DefaultRole string `mapstructure:"APP_AUTH_DEFAULT_ROLE"`
}
