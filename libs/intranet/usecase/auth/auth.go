// Package auth provides user authentication and authorization functionality for the SA Intranet application.
// It implements Clean Architecture principles with separate layers for models, repositories, interactors,
// presenters, and services. The package handles user management, role-based access control, and user provisioning.
package auth

import (
	"sa-intranet/usecase/auth/config"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service"

	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"

	"github.com/samber/do"
)

// Register initializes and registers all authentication-related services with the dependency injection container.
// This function sets up the complete authentication system including repositories, interactors, presenters,
// and services following Clean Architecture patterns.
//
// The registration includes:
//   - Configuration management for authentication settings
//   - User repository for data persistence
//   - Input ports (interactors) for business logic
//   - Output ports (presenters) for response formatting
//   - Main user service for orchestration
//   - User provisioning service for automated user management
//
// Parameters:
//   - i: The dependency injection container
//   - authConfig: Configuration settings for the authentication system
//
// Returns an error if any service registration fails.
func Register(i *do.Injector, authConfig config.AuthConfig) error {
	// Register authentication configuration for use throughout the auth system
	do.Provide(i, func(i *do.Injector) (config.AuthConfig, error) {
		return authConfig, nil
	})

	// Register user repository for data persistence operations
	do.Provide(i, repository.NewUserDefaultRepository)

	// Register input ports (interactors) for business logic operations
	do.Provide(i, in.NewCreateUserInteractor)
	do.Provide(i, in.NewUpdateUserInteractor)
	do.Provide(i, in.NewListUserInteractor)
	do.Provide(i, in.NewUpdateUserRoleInteractor)

	// Register output ports (presenters) for response formatting
	do.Provide(i, out.NewCreateUserPresenter)
	do.Provide(i, out.NewUpdateUserPresenter)
	do.Provide(i, out.NewListUserPresenter)
	do.Provide(i, out.NewUpdateUserRolePresenter)

	// Register main user service for orchestrating auth operations
	do.Provide(i, service.NewUserService)

	// Register user provisioning service for automated user management
	do.Provide(i, in.NewUserProvisioningService)

	return nil
}
